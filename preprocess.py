#!/usr/bin/env python3
"""
preprocess.py

Reads:  data/synthetic_patients_last_record.csv
Writes: outputs/preprocessed_last_record.csv
       outputs/preprocessor_scaler.joblib

What it does:
 - Loads last-record CSV produced by data_generator.py
 - Safety: drops obvious leakage columns (regex match) if present
 - Feature engineering: binary flags (high_bnp, low_spo2, poor_adherence, uncontrolled_hba1c),
   interaction features, basic ratios where useful.
 - Encodes categoricals with one-hot (safe, no target leakage)
 - Imputes numeric missing values with median
 - Scales numeric features with StandardScaler (saves scaler)
 - Keeps meta columns patient_id, record_date (unsclaed) for reference
 - Exposes 'target' column expected by training scripts (renames deterioration_90d -> target)
"""

import os
import re
import joblib
import numpy as np
import pandas as pd
from sklearn.impute import SimpleImputer
from sklearn.preprocessing import StandardScaler

INPUT = "data/synthetic_patients_last_record.csv"
OUT_DIR = "outputs"
OUT_CSV = os.path.join(OUT_DIR, "preprocessed_last_record.csv")
SCALER_PATH = os.path.join(OUT_DIR, "preprocessor_scaler.joblib")

os.makedirs(OUT_DIR, exist_ok=True)

print("Loading data from:", INPUT)
df = pd.read_csv(INPUT)

# ---------------- Safety: drop potential leakage columns ----------------
# any column name that contains these tokens will be dropped to avoid label leakage
leakage_tokens = ["prob", "_raw", "deterioration_90d_y", "deterioration_prob", "outcome", "label_prob"]
drop_leak = [c for c in df.columns if any(tok in c.lower() for tok in leakage_tokens)]
if drop_leak:
    print("Warning: dropping potential leakage columns:", drop_leak)
    df = df.drop(columns=drop_leak, errors='ignore')

# ---------------- Meta columns ----------------
meta_cols = []
for c in ["patient_id", "record_date"]:
    if c in df.columns:
        meta_cols.append(c)

# ---------------- Target ----------------
# Accept either 'deterioration_90d' or 'deterioration_90d' variants
target_col_candidates = [c for c in df.columns if re.fullmatch(r"(deterioration_90d|target|label)", c, flags=re.I)]
if "deterioration_90d" in df.columns:
    df = df.rename(columns={"deterioration_90d": "target"})
elif len(target_col_candidates) >= 1:
    # choose first candidate and rename to 'target'
    df = df.rename(columns={target_col_candidates[0]: "target"})
else:
    raise RuntimeError("No target column found (expected 'deterioration_90d' or similar).")

# Ensure target is integer 0/1
df["target"] = df["target"].fillna(0).astype(int)

# ---------------- Basic cleaning ----------------
# Trim whitespace in string columns
for c in df.select_dtypes(include=["object"]).columns:
    df[c] = df[c].astype(str).str.strip()

# ---------------- Feature engineering ----------------
# create clinically useful binary flags and interactions (non-leaking)
def add_features(df):
    out = df.copy()

    # binary flags
    out["high_bnp"] = (out["max_bnp"] > 300).astype(int) if "max_bnp" in out.columns else 0
    out["low_spo2"] = (out["min_spo2"] < 92).astype(int) if "min_spo2" in out.columns else 0
    out["poor_adherence"] = (out["mean_med_adherence"] < 70).astype(int) if "mean_med_adherence" in out.columns else 0
    out["uncontrolled_hba1c"] = (out["mean_hba1c"] > 9).astype(int) if "mean_hba1c" in out.columns else 0
    out["rapid_weight_gain"] = (out["slope_weight"] > 2.0).astype(int) if "slope_weight" in out.columns else 0
    out["many_ed_visits"] = (out["prior_ed_visits_90d"] >= 1).astype(int) if "prior_ed_visits_90d" in out.columns else 0

    # simple interactions (clinically meaningful)
    if "max_bnp" in out.columns and "slope_weight" in out.columns:
        out["bnp_x_slope_weight"] = out["max_bnp"] * out["slope_weight"]
    if "min_spo2" in out.columns and "mean_med_adherence" in out.columns:
        out["spo2_x_adherence"] = out["min_spo2"] * out["mean_med_adherence"]
    if "bp_sys" in out.columns and "age" in out.columns:
        out["bp_age_interaction"] = out["bp_sys"] * out["age"]

    # comorbidity_count: if you have separate condition flags, produce count; otherwise 1
    # if 'condition' present, we'll create one-hot later; create a numeric fallback
    out["comorbidity_count"] = 1  # synthetic single-condition patients -> keep 1
    return out

df = add_features(df)

# ---------------- Categorical encoding ----------------
# Keep meta cols aside then encode categorical columns with one-hot encoding
cat_cols = []
for c in df.select_dtypes(include=["object"]).columns.tolist():
    if c in meta_cols or c == "target":
        continue
    cat_cols.append(c)

# We'll only one-hot encode known categorical features: 'sex', 'condition', 'med_class' if present
to_ohe = [c for c in ["sex", "condition", "med_class"] if c in df.columns]
print("One-hot encoding:", to_ohe)
df_ohe = pd.get_dummies(df.drop(columns=meta_cols + ["target"], errors="ignore"), columns=to_ohe, drop_first=False)

# ---------------- Numeric columns & imputation ----------------
# Identify numeric columns (after OHE)
numeric_cols = df_ohe.select_dtypes(include=[np.number]).columns.tolist()
print(f"Numeric cols count: {len(numeric_cols)}")

# Impute missing numeric with median (simple & robust)
imputer = SimpleImputer(strategy="median")
X_numeric = imputer.fit_transform(df_ohe[numeric_cols])
X_numeric = pd.DataFrame(X_numeric, columns=numeric_cols, index=df_ohe.index)

# ---------------- Scaling ----------------
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X_numeric)
X_scaled = pd.DataFrame(X_scaled, columns=numeric_cols, index=df_ohe.index)

# ---------------- Final dataset assembly ----------------
# Keep meta columns unscaled for reference, attach scaled numeric features, and target
final_df = pd.DataFrame()
for m in meta_cols:
    if m in df.columns:
        final_df[m] = df[m].values
# append scaled features
for c in numeric_cols:
    final_df[c] = X_scaled[c].values
# append target
final_df["target"] = df["target"].values

# Save preprocessed CSV and scaler/imputer metadata
final_df.to_csv(OUT_CSV, index=False)
joblib.dump({
    "scaler": scaler,
    "imputer": imputer,
    "numeric_cols": numeric_cols,
    "meta_cols": meta_cols
}, SCALER_PATH)

print("Preprocessing complete.")
print("Saved preprocessed CSV ->", OUT_CSV)
print("Saved preprocessor ->", SCALER_PATH)
print("Sample of preprocessed features:")
print(final_df.head().T.iloc[:60])
