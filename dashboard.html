<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width,initial-scale=1"/>
<title>Chronic Care Risk Dashboard</title>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<style>
  :root{
    --bg:#071428;
    --card:#0f1b2b;
    --muted:#9fb0c8;
    --accent:#6c8cff;
    --success:#7be3b6;
    --danger:#ff7b7b;
    --warn:#ffb86b;
    --glass: rgba(255,255,255,0.03);
    --text:#ecf6ff;
  }
  html,body{height:100%;margin:0;background:
    radial-gradient(circle at 10% 10%, #081028 0%, #041224 50%, #02101a 100%); color:var(--text);
    font-family: Inter, ui-sans-serif, system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial;}
  .wrap{max-width:1200px;margin:26px auto;padding:18px}
  header{display:flex;justify-content:space-between;align-items:center;margin-bottom:18px}
  .brand{display:flex;gap:12px;align-items:center}
  .logo{width:56px;height:56px;border-radius:12px;background:linear-gradient(135deg,var(--accent),#5b86ff);display:flex;align-items:center;justify-content:center;font-weight:800;color:#06203b}
  h1{margin:0;font-size:20px}
  p.lead{margin:0;color:var(--muted);font-size:13px}
  .grid{display:grid;grid-template-columns:1fr 420px;gap:18px}
  .card{background:linear-gradient(180deg,var(--card), rgba(8,20,34,0.8));border-radius:12px;padding:14px;border:1px solid rgba(255,255,255,0.03)}
  .controls{display:flex;gap:10px;align-items:center;margin-bottom:12px}
  .input, select, button{padding:8px 10px;border-radius:8px;background:transparent;border:1px solid rgba(255,255,255,0.05);color:var(--text)}
  .btn-primary{background:linear-gradient(90deg,var(--accent),#5b86ff);border:none;padding:8px 12px;border-radius:8px;color:#02102b;font-weight:700}
  table{width:100%;border-collapse:collapse;font-size:14px}
  thead th{color:var(--muted);font-size:12px;text-align:left;padding:8px 6px;border-bottom:1px solid rgba(255,255,255,0.03)}
  tbody td{padding:10px 6px;border-bottom:1px solid rgba(255,255,255,0.02)}
  tr:hover td{background:linear-gradient(90deg, rgba(255,255,255,0.01), rgba(255,255,255,0.005));cursor:pointer}
  .risk-pill{display:inline-block;padding:6px 10px;border-radius:999px;font-weight:800;font-size:13px}
  .risk-high{background:linear-gradient(90deg,#ff9a9a,#ff5f5f);color:#2b0000}
  .risk-med{background:linear-gradient(90deg,#ffd88a,#ffb84d);color:#331900}
  .risk-low{background:linear-gradient(90deg,#9bf7c9,#56d89f);color:#023a26}
  .shap-grid{display:grid;grid-template-columns:repeat(2,1fr);gap:10px}
  .shap-item{background:rgba(255,255,255,0.02);padding:10px;border-radius:8px;border:1px solid rgba(255,255,255,0.02);font-size:13px}
  .muted{color:var(--muted);font-size:13px}
  .small{font-size:13px}
  .sidebar .section{margin-bottom:12px}
  .chart-wrap{height:140px;padding:8px;background:rgba(255,255,255,0.01);border-radius:8px}
  .list{margin:8px 0;padding-left:18px;color:var(--muted)}
  footer{margin-top:16px;color:var(--muted);font-size:12px;text-align:center}
  @media (max-width:1000px){.grid{grid-template-columns:1fr}}
</style>
</head>
<body>
<div class="wrap">
  <header>
    <div class="brand">
      <div class="logo">RP</div>
      <div>
        <h1>AI Risk Prediction — Chronic Care</h1>
        <p class="lead">Predict deterioration in next 90 days • Ensemble model • Explainability (SHAP)</p>
      </div>
    </div>
    <div style="display:flex;gap:10px;align-items:center">
      <div class="muted">Use Live Server for CSV loading</div>
      <button class="btn-primary" id="exportBtn">Export cohort CSV</button>
    </div>
  </header>

  <div class="grid">
    <!-- Left: cohort + global importance -->
    <div>
      <div class="card">
        <div class="controls">
          <input id="search" class="input" placeholder="Search patient id or date..."/>
          <select id="bucket" class="input">
            <option value="all">All risk buckets</option>
            <option value="high">High (≥60%)</option>
            <option value="medium">Medium (30–60%)</option>
            <option value="low">Low (&lt;30%)</option>
          </select>
          <select id="sort" class="input">
            <option value="risk_desc">Sort: Risk ↓</option>
            <option value="risk_asc">Sort: Risk ↑</option>
            <option value="id">Sort: Patient ID</option>
          </select>
          <div style="margin-left:auto" class="muted small" id="cohortCount">— patients</div>
        </div>

        <div style="max-height:420px;overflow:auto;border-radius:8px;padding-right:6px">
          <table id="cohortTable" aria-label="Cohort">
            <thead><tr><th>Patient</th><th>Risk</th><th>Action</th><th class="muted">Last</th></tr></thead>
            <tbody></tbody>
          </table>
        </div>
      </div>

      <div class="card">
        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:8px">
          <div><strong>Global feature importance (SHAP)</strong><div class="muted small">Top drivers across cohort</div></div>
          <div class="muted small" id="shapNote">Source: shap_global.csv</div>
        </div>
        <div id="shapGrid" class="shap-grid"></div>
      </div>
    </div>

    <!-- Right: patient detail -->
    <aside class="sidebar">
      <div class="card section">
        <div style="display:flex;justify-content:space-between;align-items:center">
          <div>
            <div class="muted small">Selected patient</div>
            <div id="pid" style="font-weight:800">—</div>
          </div>
          <div style="text-align:right">
            <div class="muted small">Predicted 90d risk</div>
            <div id="riskLarge" style="font-weight:900;font-size:22px">—</div>
          </div>
        </div>

        <div id="driverBox" style="margin-top:12px">
          <div class="muted small">Key drivers (local)</div>
          <div id="localDrivers" style="margin-top:8px"></div>
        </div>

        <div style="margin-top:12px">
          <div class="muted small">Recommended actions</div>
          <ul id="actions" class="list"></ul>
        </div>

        <div style="margin-top:12px">
          <div class="muted small">Trend snapshots</div>
          <div style="display:grid;gap:8px;margin-top:8px">
            <div class="chart-wrap"><canvas id="chartWeight"></canvas></div>
            <div class="chart-wrap"><canvas id="chartBNP"></canvas></div>
            <div class="chart-wrap"><canvas id="chartSpO2"></canvas></div>
            <div class="chart-wrap"><canvas id="chartAdh"></canvas></div>
          </div>
        </div>
      </div>

      <div class="card">
        <strong>Quick actions</strong>
        <ol class="list">
          <li>Confirm vitals and medication list in EHR</li>
          <li>Nurse phone triage within 48 hours for high-risk</li>
          <li>Arrange home visit / med review for volume overload signals</li>
        </ol>
      </div>
    </aside>
  </div>

</div>

<script>
/*
  Single-file dashboard:
  - default CSV paths (edit if needed)
  - robust CSV parser that handles quoted fields
  - populates cohort table and patient details (risk, local drivers, charts)
  - if timeseries CSV missing, shows demo trends
*/

const COHORT_CSV = 'outputs/final_results_v3/oof_stack_preds.csv';
const SHAP_CSV = 'outputs/final_results_v3/shap_global.csv';
const TIMESERIES_CSV = 'data/synthetic_patients_timeseries.csv';

let cohort = [];       // array of objects (cohort rows)
let shapGlobal = [];   // array of {feature, shap_mean_abs}
let timeseries = [];   // timeseries rows

// Robust CSV parser (handles quoted commas)
function csvToObjects(text){
  if(!text) return [];
  const rows = [];
  const lines = text.split(/\r?\n/).filter(l=>l.trim().length>0);
  if(lines.length===0) return [];
  const headers = parseCsvLine(lines[0]);
  for(let i=1;i<lines.length;i++){
    const vals = parseCsvLine(lines[i]);
    const obj = {};
    for(let j=0;j<headers.length;j++){
      obj[headers[j]] = vals[j] !== undefined ? vals[j] : '';
    }
    rows.push(obj);
  }
  return rows;
}

function parseCsvLine(line){
  const out=[]; let cur=''; let inQuotes=false;
  for(let i=0;i<line.length;i++){
    const ch=line[i];
    if(ch==='\"'){ if(inQuotes && line[i+1]==='\"'){ cur+='\"'; i++; } else inQuotes=!inQuotes; continue; }
    if(ch===',' && !inQuotes){ out.push(cur); cur=''; continue; }
    cur+=ch;
  }
  out.push(cur);
  return out.map(s=>s.trim());
}

async function loadCsvPath(path){
  try{
    const res = await fetch(path);
    if(!res.ok) throw new Error('HTTP '+res.status);
    const txt = await res.text();
    return csvToObjects(txt);
  } catch(e){
    console.warn('Failed to load CSV:', path, e);
    return null;
  }
}

function toNumber(v){ const n = Number(v); return isNaN(n) ? null : n; }

function detectRiskField(row){
  // common names used earlier: oof_stack, oof_stack_raw, oof_stack_cal, deterioration_prob
  const keys = Object.keys(row);
  const candidates = keys.filter(k => /oof|prob|score|risk/i.test(k));
  // prefer exact matches
  for(const k of ['oof_stack','oof_stack_raw','oof_stack_cal','oof_stack_calibrated','deterioration_prob','risk']) if(row[k]!==undefined) return k;
  // otherwise choose candidate with numeric values
  for(const k of candidates){
    if(toNumber(row[k])!==null) return k;
  }
  return null;
}

function detectIdField(row){
  const keys = Object.keys(row);
  for(const k of ['patient_id','patientid','id']) if(k in row) return k;
  for(const k of keys) if(/patient/i.test(k)) return k;
  return keys[0]; // fallback
}

function detectDateField(row){
  const keys = Object.keys(row);
  for(const k of ['record_date','date','recorddate']) if(k in row) return k;
  for(const k of keys) if(/date/i.test(k)) return k;
  return null;
}

// UI helpers
function riskClass(p){
  if(p>=0.6) return 'risk-high';
  if(p>=0.3) return 'risk-med';
  return 'risk-low';
}
function fmtPct(p){ if(p===null) return '—'; return (p*100).toFixed(1) + '%'; }

function renderCohortTable(rows){
  const tbody = document.querySelector('#cohortTable tbody');
  tbody.innerHTML = '';
  rows.forEach(r=>{
    const tr = document.createElement('tr');
    const pid = r._id || r.id || r.patient_id || r.patientid || r.patient;
    const risk = toNumber(r._risk);
    const action = (risk>=0.6) ? 'Urgent' : (risk>=0.3 ? 'Review' : 'Routine');
    const pill = `<span class="risk-pill ${riskClass(risk)}">${fmtPct(risk)}</span>`;
    const last = r._date || r.record_date || r.date || '';
    tr.innerHTML = `<td>${pid||''}</td><td>${pill}</td><td><span class="${action==='Urgent'?'risk-high':action==='Review'?'risk-med':'risk-low'} badge">${action}</span></td><td class="muted small">${last}</td>`;
    tr.onclick = ()=> selectPatient(r);
    tbody.appendChild(tr);
  });
  document.getElementById('cohortCount').innerText = `${rows.length} patients`;
}

function renderShapGlobal(shap){
  const el = document.getElementById('shapGrid'); el.innerHTML = '';
  if(!shap || shap.length===0){ el.innerHTML = '<div class="muted">No SHAP file found</div>'; return; }
  shap.slice(0,12).forEach(s=>{
    const name = s.feature || s.Feature || Object.keys(s)[0];
    const val = toNumber(s.shap_mean_abs || s.shap_mean || s['shap_mean_abs']);
    const div = document.createElement('div'); div.className='shap-item';
    div.innerHTML = `<div class="muted small">${name}</div><div style="font-weight:800">${val!==null?val.toFixed(3):'-'}</div>`;
    el.appendChild(div);
  });
}

// selection -> show detail panel
function selectPatient(row){
  // row is raw cohort row; collapse to canonical object
  const pid = row._id || row.id || row.patient_id || row.patientid || row.patient;
  document.getElementById('pid').innerText = pid || '—';
  const risk = toNumber(row._risk);
  document.getElementById('riskLarge').innerText = fmtPct(risk);

  // build recommended actions
  const actionsEl = document.getElementById('actions'); actionsEl.innerHTML = '';
  const bucket = (risk>=0.6) ? 'high' : (risk>=0.3 ? 'medium' : 'low');
  const actions = bucket==='high' ? ['Urgent nurse outreach within 48h','Review diuretic & fluid plan','Consider home visit or ED referral']
                 : bucket==='medium' ? ['Schedule case manager call','Increase remote vitals monitoring','Confirm medication adherence']
                 : ['Routine tele-check-in in 2 weeks','Reinforce adherence','Next routine appointment'];
  actions.forEach(a=>{ const li=document.createElement('li'); li.innerText = a; actionsEl.appendChild(li); });

  // local drivers: try to compute using shapGlobal top features + cohort numeric columns
  const localBox = document.getElementById('localDrivers'); localBox.innerHTML = '';
  if(shapGlobal && shapGlobal.length>0){
    // top 4 global features
    const top = shapGlobal.slice(0,6).map(s => (s.feature || s.Feature || Object.keys(s)[0]));
    // compute simple z-score compared to cohort numeric medians/stds if available
    const numericCols = cachedNumericCols || inferNumericColumns(cohort);
    const medians = computeMedians(cohort, numericCols);
    const stds = computeStds(cohort, numericCols);
    let added=0;
    for(const feat of top){
      // some SHAP features might be named like 'max_bnp' or 'high_bnp' etc.
      if(feat in row){
        const val = toNumber(row[feat]);
        if(val===null) continue;
        const z = stds[feat]>0 ? ((val - medians[feat]) / stds[feat]) : 0;
        const dir = z>0 ? '↑ higher than cohort' : z<0 ? '↓ lower than cohort' : '≈ typical';
        const item = document.createElement('div');
        item.innerHTML = `<strong>${feat}</strong><div class="muted small">${val} — ${dir} (z=${z.toFixed(2)})</div>`;
        localBox.appendChild(item);
        added++;
      }
      if(added>=4) break;
    }
    if(added===0){
      // fallback: show top global features values absent per-patient
      top.slice(0,4).forEach(feat=>{
        const item = document.createElement('div');
        item.innerHTML = `<strong>${feat}</strong><div class="muted small">Important driver (patient value not available)</div>`;
        localBox.appendChild(item);
      });
    }
  } else {
    localBox.innerHTML = '<div class="muted">No SHAP available — global drivers shown instead.</div>';
    // fallback: show a few columns if present
    const keys = Object.keys(row).slice(0,6);
    keys.forEach(k=>{
      const item=document.createElement('div');
      item.innerHTML = `<strong>${k}</strong><div class="muted small">${row[k]}</div>`;
      localBox.appendChild(item);
    });
  }

  // charts: use timeseries if available else demo
  const ts = timeseries.filter(t => (t.patient_id || t.patientid) === pid);
  if(ts && ts.length>0){
    // sort by date
    ts.sort((a,b)=> new Date(a.record_date) - new Date(b.record_date));
    updateCharts(ts);
  } else {
    updateCharts(generateDemoTrends(risk));
  }
}

// helpers for numeric medians/stds
let cachedNumericCols = null;
function inferNumericColumns(rows){
  const keys = Object.keys(rows[0] || {});
  const numeric = {};
  keys.forEach(k=>{
    for(let r of rows){
      if(r[k]===undefined) continue;
      const n = Number(r[k]);
      if(!isNaN(n)){ numeric[k]=true; break;}
    }
  });
  cachedNumericCols = Object.keys(numeric);
  return cachedNumericCols;
}
function computeMedians(rows, cols){
  const med = {};
  cols.forEach(c=>{
    const vals = rows.map(r=> toNumber(r[c])).filter(v=>v!==null);
    if(vals.length===0) med[c]=0; else{
      vals.sort((a,b)=>a-b);
      const mid = Math.floor(vals.length/2);
      med[c] = vals.length%2? vals[mid] : (vals[mid-1]+vals[mid])/2;
    }
  });
  return med;
}
function computeStds(rows, cols){
  const s = {};
  cols.forEach(c=>{
    const vals = rows.map(r=> toNumber(r[c])).filter(v=>v!==null);
    if(vals.length===0) s[c]=0; else{
      const mean = vals.reduce((a,b)=>a+b,0)/vals.length;
      const varr = vals.reduce((acc,v)=> acc + Math.pow(v-mean,2), 0)/vals.length;
      s[c] = Math.sqrt(varr) || 0;
    }
  });
  return s;
}

// chart.js setup
let chartWeight, chartBNP, chartSpO2, chartAdh;
function initCharts(){
  const cfg = (label, color)=>({
    type:'line',
    data:{labels:[],datasets:[{label, data:[], borderColor:color, backgroundColor:'rgba(0,0,0,0)', tension:0.3, pointRadius:2}]},
    options:{
      plugins:{legend:{display:false}},
      scales:{x:{display:false},y:{ticks:{color: '#cfe7ff'}}}
    }
  });
  chartWeight = new Chart(document.getElementById('chartWeight'), cfg('Weight (kg)','rgba(108,140,255,0.95)'));
  chartBNP = new Chart(document.getElementById('chartBNP'), cfg('BNP (pg/mL)','rgba(124,237,179,0.95)'));
  chartSpO2 = new Chart(document.getElementById('chartSpO2'), cfg('SpO₂ (%)','rgba(96,165,250,0.95)'));
  chartAdh = new Chart(document.getElementById('chartAdh'), cfg('Adherence (%)','rgba(236,72,153,0.95)'));
}

function updateCharts(ts){ // ts: array of {record_date,weight_kg,bnp,spo2,med_adherence_pct}
  const labels = ts.map(r => r.record_date || '');
  chartWeight.data.labels = labels;
  chartWeight.data.datasets[0].data = ts.map(r => toNumber(r.weight_kg) || toNumber(r.weight) || 0);
  chartBNP.data.labels = labels;
  chartBNP.data.datasets[0].data = ts.map(r => toNumber(r.bnp) || 0);
  chartSpO2.data.labels = labels;
  chartSpO2.data.datasets[0].data = ts.map(r => toNumber(r.spo2) || toNumber(r.min_spo2) || 0);
  chartAdh.data.labels = labels;
  chartAdh.data.datasets[0].data = ts.map(r => toNumber(r.med_adherence_pct) || toNumber(r.mean_med_adherence) || 0);
  chartWeight.update(); chartBNP.update(); chartSpO2.update(); chartAdh.update();
}

// demo trend generator
function generateDemoTrends(risk){
  const out=[];
  const now = new Date();
  for(let i=5;i>=0;i--){
    const d = new Date(now.getFullYear(), now.getMonth()-i, 1);
    const date = d.toISOString().slice(0,10);
    const weight = 70 + risk*20 + (Math.random()*4-2);
    const bnp = Math.max(10, Math.round(120 + risk*500 + (Math.random()*80-40)));
    const spo2 = Math.max(80, Math.round(98 - risk*12 + (Math.random()*3-1)));
    const adh = Math.max(30, Math.round(90 - risk*50 + (Math.random()*8-4)));
    out.push({record_date:date, weight_kg:weight.toFixed(1), bnp, spo2, med_adherence_pct:adh});
  }
  return out;
}

// load everything and connect UI
async function boot(){
  initCharts();

  // load cohort
  const c = await loadCsvPath(COHORT_CSV);
  if(!c){ document.querySelector('#cohortTable tbody').innerHTML='<tr><td colspan="4" class="muted">Cohort CSV not found — place oof_stack_preds.csv in outputs/final_results_v3/ or edit COHORT_CSV path.</td></tr>'; return; }
  // detect fields and normalize
  const idField = detectIdField(c[0]);
  const dateField = detectDateField(c[0]) || 'record_date';
  const riskField = detectRiskField(c[0]) || detectRiskField(c[1]||c[0]) || null;

  cohort = c.map(r => {
    const obj = Object.assign({}, r);
    obj._id = r[idField] || r.patient_id || r.patientid || r.id;
    obj._date = r[dateField] || r.record_date || '';
    obj._risk = riskField ? (toNumber(r[riskField]) || 0) : 0;
    return obj;
  });

  // load shap global
  const s = await loadCsvPath(SHAP_CSV);
  shapGlobal = s || [];

  // load timeseries optional
  const ts = await loadCsvPath(TIMESERIES_CSV);
  timeseries = (ts || []).map(r=>{
    return {
      patient_id: r.patient_id || r.patientid || r._id || r.id,
      record_date: r.record_date || r.date,
      weight_kg: r.weight_kg || r.weight,
      bnp: r.bnp,
      spo2: r.spo2 || r.min_spo2,
      med_adherence_pct: r.med_adherence_pct || r.mean_med_adherence
    };
  });

  // render UI
  renderShapGlobal(shapGlobal);
  // cache numeric columns for local driver z-scores
  if(cohort.length>0){
    inferNumericColumns(cohort);
  }
  refreshTable();

  // select top risk patient by default
  const top = cohort.slice().sort((a,b)=> b._risk - a._risk)[0];
  if(top) selectPatient(top);
}

function refreshTable(){
  const q = document.getElementById('search').value.toLowerCase();
  const bucket = document.getElementById('bucket').value;
  const sort = document.getElementById('sort').value;
  let rows = cohort.slice();
  if(q){
    rows = rows.filter(r => (r._id && r._id.toLowerCase().includes(q)) || (r._date && r._date.toLowerCase().includes(q)));
  }
  if(bucket!=='all'){
    rows = rows.filter(r=> {
      if(r._risk>=0.6) return bucket==='high';
      if(r._risk>=0.3) return bucket==='medium';
      return bucket==='low';
    });
  }
  if(sort==='risk_desc') rows.sort((a,b)=> b._risk - a._risk);
  else if(sort==='risk_asc') rows.sort((a,b)=> a._risk - b._risk);
  else if(sort==='id') rows.sort((a,b)=> (a._id||'').localeCompare(b._id||''));
  renderCohortTable(rows);
}

// UI bindings
document.getElementById('search').addEventListener('input', refreshTable);
document.getElementById('bucket').addEventListener('change', refreshTable);
document.getElementById('sort').addEventListener('change', refreshTable);
document.getElementById('exportBtn').addEventListener('click', ()=>{
  if(cohort.length===0){ alert('No cohort loaded'); return; }
  const rows = cohort.map(r=> `${r._id||''},${r._risk||''},${r._date||''}`).join('\\n');
  const csv = 'patient_id,oof_stack,record_date\\n' + rows;
  const blob = new Blob([csv], {type:'text/csv'});
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a'); a.href = url; a.download='cohort_export.csv'; a.click(); URL.revokeObjectURL(url);
});

// boot
boot();

</script>
</body>
</html>
