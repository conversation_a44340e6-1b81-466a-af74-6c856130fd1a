# -------------------------
# Python & Environment
# -------------------------
__pycache__/
*.py[cod]
*.pyo
*.pyd
*.so
*.egg-info/
*.egg
*.manifest
*.spec
.Python
.env
.venv/
venv/
ENV/
env/
.ipynb_checkpoints
.DS_Store

# -------------------------
# IDE / Editor
# -------------------------
.vscode/
.idea/
*.swp
*.swo

# -------------------------
# Build / Logs
# -------------------------
build/
dist/
*.log
*.out
*.tmp

# -------------------------
# Data (ignore raw/interim, keep final synthetic)
# -------------------------
data/raw/
data/interim/
data/cache/
*.h5
*.npy
*.npz
*.pkl
*.joblib

# -------------------------
# Outputs (ignore temp, keep final results)
# -------------------------
outputs/*.tmp
outputs/*.log

# === Keep these important outputs ===
!outputs/final_results_v3/oof_stack_preds.csv
!outputs/final_results_v3/*.csv
!outputs/final_results_v3/*.png
!outputs/final_results_v3/models_bundle.joblib

# -------------------------
# Project Files (always keep)
# -------------------------
!data/synthetic_patients_last_record.csv
!data/synthetic_patients_timeseries.csv
!data_generator.py
!preprocess.py
!train_and_eval_final_v3.py
!app.py
!requirements.txt
!README.md
