# app_streamlit.py
"""
Streamlit demo (auto-loads saved model, data, and SHAP values).
Run:
    streamlit run app_streamlit.py
"""
import streamlit as st
import pandas as pd
import numpy as np
import joblib
import os
import warnings
from datetime import datetime, timedelta

# Optional imports for model scoring
try:
    import xgboost as xgb
except ImportError:
    xgb = None
    warnings.warn("XGBoost not available. XGBoost models will not work.")

try:
    import lightgbm as lgb
except ImportError:
    lgb = None
    warnings.warn("LightGBM not available. LightGBM models will not work.")

try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.linear_model import LogisticRegression
except ImportError:
    warnings.warn("Scikit-learn models may not be available.")

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

# Paths (hard-coded)
MODEL_PATH = "models/gbm_model.pkl"
DATA_PATH = "data/synthetic_patients.csv"
SHAP_VALUES_CSV = os.path.join("reports", "shap", "shap_values.csv")
WINDOW_DAYS = 90

st.set_page_config(layout="wide", page_title="Risk Prediction Engine (Synthetic)")
st.title("Risk Prediction Engine — Synthetic Demo")

@st.cache_resource
def load_model():
    if not os.path.exists(MODEL_PATH):
        raise FileNotFoundError(f"Model not found at {MODEL_PATH}. Run training first.")
    return joblib.load(MODEL_PATH)

@st.cache_data
def load_data():
    if not os.path.exists(DATA_PATH):
        raise FileNotFoundError(f"Data not found at {DATA_PATH}. Run data generation first.")
    df = pd.read_csv(DATA_PATH)
    df['record_date'] = pd.to_datetime(df['record_date']) # Ensure date is in datetime format
    return df

def make_snapshot_features(df, window_days=WINDOW_DAYS):
    # Local replication of snapshot logic used in features.py
    df = df.copy()
    df['record_date'] = pd.to_datetime(df['record_date'])
    last_idx = df.groupby('patient_id')['record_date'].idxmax()
    snapshots = df.loc[last_idx].set_index('patient_id')
    rows = []
    for pid, snap in snapshots.iterrows():
        pid_rows = df[df['patient_id'] == pid]
        cutoff = snap['record_date'] - pd.Timedelta(days=window_days)
        window_rows = pid_rows[pid_rows['record_date'] >= cutoff]
        feat = {}
        feat['patient_id'] = pid
        feat['age'] = snap['age']
        feat['sex_M'] = 1 if snap['sex'] == 'M' else 0
        feat['condition_hf'] = 1 if snap['condition'] == 'heart_failure' else 0
        feat['condition_dm'] = 1 if snap['condition'] == 'diabetes' else 0
        feat['condition_copd'] = 1 if snap['condition'] == 'copd' else 0
        for col in ['bp_sys','bp_dia','hr','spo2','weight_kg','bnp','hba1c','egfr','med_diuretic_adherence_pct','med_refill_gap_days','prior_ed_visits_90d','prior_admissions_1yr']:
            if col in window_rows and not window_rows[col].empty:
                feat[f'{col}_last'] = window_rows[col].iloc[-1]
                feat[f'{col}_mean'] = window_rows[col].mean()
                feat[f'{col}_std'] = window_rows[col].std() if len(window_rows)>1 else 0.0
                feat[f'{col}_min'] = window_rows[col].min()
                feat[f'{col}_max'] = window_rows[col].max()
                if len(window_rows) > 1:
                    delta = (window_rows[col].iloc[-1] - window_rows[col].iloc[0])
                    days = (window_rows['record_date'].iloc[-1] - window_rows['record_date'].iloc[0]).days
                    feat[f'{col}_slope_per_day'] = delta / max(days,1)
                else:
                    feat[f'{col}_slope_per_day'] = 0.0
            else:
                feat[f'{col}_last'] = np.nan
                feat[f'{col}_mean'] = np.nan
                feat[f'{col}_std'] = np.nan
                feat[f'{col}_min'] = np.nan
                feat[f'{col}_max'] = np.nan
                feat[f'{col}_slope_per_day'] = 0.0
        feat['outcome'] = int(snap.get('outcome_hospitalization_90d', 0))
        rows.append(feat)
    X = pd.DataFrame(rows).set_index('patient_id')
    y = X['outcome']
    X = X.drop(columns=['outcome'])
    for c in X.select_dtypes(include=[np.number]).columns:
        X[c] = X[c].fillna(X[c].median())
    return X, y

# Load
try:
    bundle = load_model()
    df = load_data()
    X, y = make_snapshot_features(df, window_days=WINDOW_DAYS)
except Exception as e:
    st.error(f"Load error: {e}")
    st.stop()

feature_cols = bundle['feature_columns']
model_obj = bundle['model']
model_type = bundle.get('model_type','xgboost')

# scoring
def score_df(X_local):
    try:
        if model_type == 'xgboost':
            if xgb is None:
                st.error("XGBoost is required but not available. Please install xgboost.")
                return np.zeros(len(X_local))
            return model_obj.predict(xgb.DMatrix(X_local[feature_cols]))
        else:
            return model_obj.predict_proba(X_local[feature_cols])[:,1]
    except Exception as e:
        st.error("Scoring failed: "+str(e))
        return np.zeros(len(X_local))

scores = score_df(X)
cohort = pd.DataFrame({'patient_id': X.index, 'risk_score': scores})
cohort['risk_band'] = pd.cut(cohort['risk_score'], bins=[-0.01,0.2,0.5,1.0], labels=['Low','Medium','High'])

st.sidebar.header("Filters")
cond_filter = st.sidebar.multiselect("Condition", options=['all','heart_failure','diabetes','copd'], default=['all'])
if 'all' not in cond_filter:
    pids_filtered = df[df['condition'].isin(cond_filter)]['patient_id'].unique()
    cohort = cohort[cohort['patient_id'].isin(pids_filtered)]

st.header("Cohort (sorted by risk)")
st.dataframe(cohort.sort_values('risk_score', ascending=False).reset_index(drop=True))

st.header("Patient detail")
pid = st.selectbox("Select patient", cohort['patient_id'].tolist())
if pid:
    score_val = float(cohort.set_index('patient_id').loc[pid,'risk_score'])
    band = cohort.set_index('patient_id').loc[pid,'risk_band']
    st.markdown(f"**Risk score:** {score_val:.2f} — **{band}**")

    # load SHAP values csv if available and show top drivers
    if os.path.exists(SHAP_VALUES_CSV):
        sv = pd.read_csv(SHAP_VALUES_CSV, index_col=0)
        if pid in sv.index:
            row = sv.loc[pid].sort_values(ascending=False)
            st.subheader("Top positive drivers (increase risk)")
            st.table(row[row>0].head(6).rename("shap_value").to_frame())
            st.subheader("Top negative drivers (decrease risk)")
            st.table(row[row<0].head(6).rename("shap_value").to_frame())
        else:
            st.info("No SHAP row for this patient in shap_values.csv.")
    else:
        st.info("SHAP values CSV not found (run explain.py).")

    # --- THIS IS THE NEWLY ADDED BLOCK ---
    st.subheader("Key Metric Trends")
    patient_history = df[df['patient_id'] == pid].set_index('record_date')
    # You can select which columns to plot
    vitals_to_plot = ['weight_kg', 'bnp', 'hba1c', 'bp_sys']
    st.line_chart(patient_history[vitals_to_plot])
    # --- END OF NEW BLOCK ---

    st.subheader("Snapshot features (latest aggregated values)")
    st.table(X.loc[pid].T.head(40))