# app_streamlit.py
"""
Streamlit demo (auto-loads saved model, data, and SHAP values).
Run:
    streamlit run app_streamlit.py
"""
import streamlit as st
import pandas as pd
import numpy as np
import joblib
import os
import warnings
from datetime import datetime, timedelta

# Optional imports for model scoring
try:
    import xgboost as xgb
except ImportError:
    xgb = None
    warnings.warn("XGBoost not available. XGBoost models will not work.")

try:
    import lightgbm as lgb
except ImportError:
    lgb = None
    warnings.warn("LightGBM not available. LightGBM models will not work.")

try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.linear_model import LogisticRegression
except ImportError:
    warnings.warn("Scikit-learn models may not be available.")

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

# Paths (hard-coded)
MODEL_PATH = "outputs/final_results_v3/models_bundle.joblib"
DATA_PATH = "data/synthetic_patients_timeseries.csv"
PREPROCESSED_DATA_PATH = "outputs/preprocessed_last_record.csv"
SHAP_VALUES_CSV = os.path.join("outputs", "final_results_v3", "shap_global.csv")
WINDOW_DAYS = 90

st.set_page_config(layout="wide", page_title="Risk Prediction Engine (Synthetic)")
st.title("Risk Prediction Engine — Synthetic Demo")

@st.cache_resource
def load_model():
    if not os.path.exists(MODEL_PATH):
        raise FileNotFoundError(f"Model not found at {MODEL_PATH}. Run training first.")
    bundle = joblib.load(MODEL_PATH)
    # Extract the meta model (ensemble) from the bundle
    meta_model = bundle['meta']
    base_models = bundle['base_models']

    # Create a simplified bundle for the app
    return {
        'model': meta_model,
        'base_models': base_models,
        'model_type': 'ensemble',
        'feature_columns': None  # Will be determined from data
    }

@st.cache_data
def load_data():
    # Load raw timeseries data for visualization
    if not os.path.exists(DATA_PATH):
        raise FileNotFoundError(f"Data not found at {DATA_PATH}. Run data generation first.")
    df_raw = pd.read_csv(DATA_PATH)
    df_raw['record_date'] = pd.to_datetime(df_raw['record_date'])

    # Load preprocessed data for modeling
    if not os.path.exists(PREPROCESSED_DATA_PATH):
        raise FileNotFoundError(f"Preprocessed data not found at {PREPROCESSED_DATA_PATH}. Run preprocessing first.")
    df_processed = pd.read_csv(PREPROCESSED_DATA_PATH)

    return df_raw, df_processed

# Feature engineering is now handled by preprocess.py
# This function is no longer needed as we use preprocessed data

# Load
try:
    bundle = load_model()
    df_raw, df_processed = load_data()

    # Prepare features and target from preprocessed data
    feature_cols = [col for col in df_processed.columns if col not in ['patient_id', 'record_date', 'target']]
    X = df_processed[feature_cols]
    y = df_processed['target']
    X.index = df_processed['patient_id']  # Set patient_id as index

except Exception as e:
    st.error(f"Load error: {e}")
    st.stop()

model_obj = bundle['model']
base_models = bundle['base_models']
model_type = bundle.get('model_type','ensemble')

# scoring
def score_df(X_local):
    try:
        if model_type == 'ensemble':
            # Generate base model predictions
            base_preds = []

            # LightGBM predictions
            if 'lgb' in base_models and len(base_models['lgb']) > 0:
                lgb_preds = np.mean([model.predict(X_local[feature_cols]) for model in base_models['lgb']], axis=0)
                base_preds.append(lgb_preds)

            # XGBoost predictions
            if 'xgb' in base_models and len(base_models['xgb']) > 0:
                if xgb is None:
                    st.warning("XGBoost models available but XGBoost not installed. Skipping XGB predictions.")
                else:
                    xgb_preds = np.mean([model.predict_proba(X_local[feature_cols])[:,1] for model in base_models['xgb']], axis=0)
                    base_preds.append(xgb_preds)

            # Random Forest predictions
            if 'rf' in base_models and len(base_models['rf']) > 0:
                rf_preds = np.mean([model.predict_proba(X_local[feature_cols])[:,1] for model in base_models['rf']], axis=0)
                base_preds.append(rf_preds)

            if len(base_preds) == 0:
                st.error("No base models available for prediction")
                return np.zeros(len(X_local))

            # Stack predictions and use meta model
            stacked_preds = np.column_stack(base_preds)
            return model_obj.predict_proba(stacked_preds)[:,1]

        elif model_type == 'xgboost':
            if xgb is None:
                st.error("XGBoost is required but not available. Please install xgboost.")
                return np.zeros(len(X_local))
            return model_obj.predict(xgb.DMatrix(X_local[feature_cols]))
        else:
            return model_obj.predict_proba(X_local[feature_cols])[:,1]
    except Exception as e:
        st.error("Scoring failed: "+str(e))
        return np.zeros(len(X_local))

scores = score_df(X)
cohort = pd.DataFrame({'patient_id': X.index, 'risk_score': scores})
cohort['risk_band'] = pd.cut(cohort['risk_score'], bins=[-0.01,0.2,0.5,1.0], labels=['Low','Medium','High'])

st.sidebar.header("Filters")
cond_filter = st.sidebar.multiselect("Condition", options=['all','heart_failure','diabetes','copd'], default=['all'])
if 'all' not in cond_filter:
    pids_filtered = df_raw[df_raw['condition'].isin(cond_filter)]['patient_id'].unique()
    cohort = cohort[cohort['patient_id'].isin(pids_filtered)]

st.header("Cohort (sorted by risk)")
st.dataframe(cohort.sort_values('risk_score', ascending=False).reset_index(drop=True))

st.header("Patient detail")
pid = st.selectbox("Select patient", cohort['patient_id'].tolist())
if pid:
    score_val = float(cohort.set_index('patient_id').loc[pid,'risk_score'])
    band = cohort.set_index('patient_id').loc[pid,'risk_band']
    st.markdown(f"**Risk score:** {score_val:.2f} — **{band}**")

    # load SHAP values csv if available and show top drivers
    if os.path.exists(SHAP_VALUES_CSV):
        try:
            sv = pd.read_csv(SHAP_VALUES_CSV)
            # The global SHAP file has feature importance, not per-patient values
            st.subheader("Global Feature Importance (SHAP)")
            st.table(sv.head(10))
        except Exception as e:
            st.info(f"Could not load SHAP values: {e}")
    else:
        st.info("SHAP values CSV not found. Global feature importance not available.")

    # --- THIS IS THE NEWLY ADDED BLOCK ---
    st.subheader("Key Metric Trends")
    patient_history = df_raw[df_raw['patient_id'] == pid].set_index('record_date')
    # You can select which columns to plot
    vitals_to_plot = ['weight_kg', 'bnp', 'hba1c', 'bp_sys']
    if not patient_history.empty:
        st.line_chart(patient_history[vitals_to_plot])
    else:
        st.info("No historical data available for this patient.")
    # --- END OF NEW BLOCK ---

    st.subheader("Snapshot features (latest aggregated values)")
    st.table(X.loc[pid].T.head(40))