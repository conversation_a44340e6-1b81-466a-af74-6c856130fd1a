#!/usr/bin/env python3
"""
train_and_eval_final_v3.py

Finalized training + evaluation + XAI script.

Outputs -> outputs/final_results_v3/*
- OOF preds (raw + calibrated)
- CSV metrics + printed metric blocks (raw + Platt + Isotonic)
- Confusion matrix images & CSVs for thresholds [0.2,0.3,0.4,0.5] + top-k cohorts
- Calibration (reliability) plots for raw + Platt + Isotonic
- SHAP global & beeswarm (simple) and permutation importance
"""
import os, warnings, joblib
warnings.filterwarnings("ignore")
import numpy as np, pandas as pd
from sklearn.model_selection import StratifiedKFold
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import (roc_auc_score, average_precision_score, brier_score_loss,
                             precision_score, recall_score, accuracy_score, f1_score,
                             confusion_matrix)
from sklearn.calibration import calibration_curve
from sklearn.inspection import permutation_importance
from sklearn.isotonic import IsotonicRegression
import lightgbm as lgb, xgboost as xgb
import shap, matplotlib.pyplot as plt, seaborn as sns
from sklearn.linear_model import LinearRegression
np.random.seed(42)

# ---------- Config ----------
PREPRO = "outputs/preprocessed_last_record.csv"
OUT = "outputs/final_results_v3"
os.makedirs(OUT, exist_ok=True)

LGB_PARAMS = {"objective":"binary","verbosity":-1,"seed":42,"num_leaves":48,"max_depth":7,"learning_rate":0.05}
XGB_PARAMS = {"objective":"binary:logistic","eval_metric":"auc","use_label_encoder":False,"seed":42,"max_depth":6,"learning_rate":0.05,"n_estimators":300}
RF_PARAMS = {"n_estimators":300,"max_depth":12,"min_samples_leaf":15,"random_state":42,"n_jobs":-1}
META = LogisticRegression(solver="lbfgs", max_iter=2000, random_state=42)

# thresholds & top-k to evaluate
THRESHOLDS = [0.2, 0.3, 0.4, 0.5]
TOP_K = [0.01, 0.05, 0.10, 0.20]

# ---------- Helpers ----------
def cal_slope_intercept(y_true, y_p):
    eps = 1e-6
    p = np.clip(y_p, eps, 1-eps)
    logit = np.log(p/(1-p)).reshape(-1,1)
    lr = LinearRegression().fit(logit, y_true)
    return float(lr.coef_[0]), float(lr.intercept_)

def save_confusion_plot(y_true, y_pred, title, fpath):
    cm = confusion_matrix(y_true, y_pred)
    fig, ax = plt.subplots(figsize=(4,3))
    sns.heatmap(cm, annot=True, fmt="d", cmap="Blues", ax=ax)
    ax.set_xlabel("Predicted")
    ax.set_ylabel("Actual")
    ax.set_title(title)
    plt.tight_layout()
    fig.savefig(fpath, dpi=150)
    plt.close(fig)
    # also save CSV
    pd.DataFrame(cm, index=["Actual_0","Actual_1"], columns=["Pred_0","Pred_1"]).to_csv(fpath.replace(".png",".csv"))

def print_metrics_block(prefix, y_true, probs, show_conf=True):
    auc = roc_auc_score(y_true, probs)
    auprc = average_precision_score(y_true, probs)
    brier = brier_score_loss(y_true, probs)
    pred05 = (probs >= 0.5).astype(int)
    acc = accuracy_score(y_true, pred05)
    prec = precision_score(y_true, pred05, zero_division=0)
    rec = recall_score(y_true, pred05, zero_division=0)
    f1 = f1_score(y_true, pred05, zero_division=0)
    slope, intercept = cal_slope_intercept(y_true, probs)
    print(f"\n--- {prefix} ---")
    print(f"AUROC: {auc:.4f}, AUPRC: {auprc:.4f}, Brier: {brier:.4f}")
    print(f"Accuracy@0.5: {acc:.4f}, Precision@0.5: {prec:.4f}, Recall@0.5: {rec:.4f}, F1@0.5: {f1:.4f}")
    print(f"Calibration slope: {slope:.4f}, intercept: {intercept:.4f}")
    if show_conf:
        tn, fp, fn, tp = confusion_matrix(y_true, pred05).ravel()
        print(f"Confusion@0.5 -> TP:{tp} FP:{fp} TN:{tn} FN:{fn}")

# ---------- Load preprocessed ---------
print("Loading preprocessed:", PREPRO)
df = pd.read_csv(PREPRO)
# safety: drop suspicious leak columns if present
leak_tokens = ["prob","_raw","deterioration_90d_y","deterioration_prob","outcome","label_prob"]
leak_cols = [c for c in df.columns if any(t in c.lower() for t in leak_tokens)]
if leak_cols:
    print("Dropping leakage columns:", leak_cols)
    df = df.drop(columns=leak_cols, errors='ignore')

# use last-record rows only if flagged
if "last_record_flag" in df.columns:
    df = df[df["last_record_flag"]==1].reset_index(drop=True)

if "target" not in df.columns:
    raise RuntimeError("No 'target' column in preprocessed file.")

meta_cols = [c for c in ("patient_id","record_date") if c in df.columns]
# keep only numeric features (safe)
X_df = df.drop(columns=meta_cols + ["target"], errors='ignore').select_dtypes(include=[np.number])
feature_names = X_df.columns.tolist()
X = X_df.values
y = df["target"].astype(int).values
n = len(y)
print(f"Loaded samples: {n}, features: {len(feature_names)}")

# ---------- Cross-validated base models (OOF preds) ----------
skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
oof = {"lgb": np.zeros(n), "xgb": np.zeros(n), "rf": np.zeros(n)}
base_models = {"lgb": [], "xgb": [], "rf": []}

fold = 0
for tr_idx, val_idx in skf.split(X, y):
    fold += 1
    print(f"\nFold {fold}/5")
    Xtr, Xval = X[tr_idx], X[val_idx]
    ytr, yval = y[tr_idx], y[val_idx]

    # LGB (callbacks)
    dtrain = lgb.Dataset(Xtr, label=ytr)
    dval = lgb.Dataset(Xval, label=yval, reference=dtrain)
    lgbm = lgb.train({**LGB_PARAMS}, dtrain, valid_sets=[dval],
                     callbacks=[lgb.early_stopping(stopping_rounds=80), lgb.log_evaluation(period=200)])
    pl = lgbm.predict(Xval, num_iteration=lgbm.best_iteration)
    oof["lgb"][val_idx] = pl
    base_models["lgb"].append(lgbm)
    lgbm.save_model(os.path.join(OUT, f"lgb_fold{fold}.txt"))

    # XGB
    xgbc = xgb.XGBClassifier(**XGB_PARAMS)
    xgbc.fit(Xtr, ytr, verbose=False)
    px = xgbc.predict_proba(Xval)[:,1]
    oof["xgb"][val_idx] = px
    base_models["xgb"].append(xgbc)
    joblib.dump(xgbc, os.path.join(OUT, f"xgb_fold{fold}.joblib"))

    # RF
    rf = RandomForestClassifier(**RF_PARAMS)
    rf.fit(Xtr, ytr)
    pr = rf.predict_proba(Xval)[:,1]
    oof["rf"][val_idx] = pr
    base_models["rf"].append(rf)
    joblib.dump(rf, os.path.join(OUT, f"rf_fold{fold}.joblib"))

    # fold metrics
    for name, preds in [("LGB",pl),("XGB",px),("RF",pr)]:
        print(f" Fold {fold} {name}: AUROC={roc_auc_score(yval,preds):.4f}, AUPRC={average_precision_score(yval,preds):.4f}")

# stack features -> meta
stackX = np.vstack([oof["lgb"], oof["xgb"], oof["rf"]]).T
meta = META
meta.fit(stackX, y)
joblib.dump(meta, os.path.join(OUT, "meta_logreg.joblib"))
oof_stack = meta.predict_proba(stackX)[:,1]

# ---------- Calibration: Platt (logistic) and Isotonic ----------
from sklearn.linear_model import LogisticRegression as LR
platt = LR(solver="lbfgs", max_iter=2000)
platt.fit(oof_stack.reshape(-1,1), y)
oof_stack_platt = platt.predict_proba(oof_stack.reshape(-1,1))[:,1]
joblib.dump(platt, os.path.join(OUT, "platt.joblib"))

# Isotonic regression fit on OOF
iso = IsotonicRegression(out_of_bounds='clip')
iso.fit(oof_stack, y)
oof_stack_iso = iso.predict(oof_stack)
joblib.dump(iso, os.path.join(OUT, "isotonic.joblib"))

# ---------- Print metric blocks ----------
print_metrics = lambda title, probs: print_metrics_block(title, y, probs)
print_metrics("STACK (raw)", oof_stack)
print_metrics("STACK (Platt calibrated)", oof_stack_platt)
print_metrics("STACK (Isotonic calibrated)", oof_stack_iso)

# ---------- Save OOF preds (CSV) ----------
oof_df = pd.DataFrame({
    **({meta_cols[0]: df[meta_cols[0]].values} if meta_cols else {"index": np.arange(n)}),
    "target": y,
    "oof_lgb": oof["lgb"], "oof_xgb": oof["xgb"], "oof_rf": oof["rf"],
    "oof_stack_raw": oof_stack, "oof_stack_platt": oof_stack_platt, "oof_stack_iso": oof_stack_iso
})
oof_csv = os.path.join(OUT, "oof_stack_preds.csv")
oof_df.to_csv(oof_csv, index=False)
print("Saved OOF preds ->", oof_csv)

# ---------- Confusion matrices at thresholds ----------
for thr in THRESHOLDS:
    for name, probs in [("raw", oof_stack), ("platt", oof_stack_platt), ("isotonic", oof_stack_iso)]:
        preds = (probs >= thr).astype(int)
        fname = os.path.join(OUT, f"confusion_{name}_thr{int(thr*100)}.png")
        save_confusion_plot(y, preds, f"Confusion {name} @ {thr:.2f}", fname)

# ---------- Top-k cohort metrics ----------
for k in TOP_K:
    topn = max(1, int(np.floor(n*k)))
    idxs = np.argsort(-oof_stack)[:topn]
    preds = np.zeros(n, dtype=int); preds[idxs] = 1
    prec = precision_score(y, preds, zero_division=0); rec = recall_score(y, preds, zero_division=0)
    print(f"Top-{int(k*100)}% flagged (N={topn}) -> Prec: {prec:.3f}, Rec: {rec:.3f}")

# ---------- Calibration plots (reliability diagrams) ----------
for name, probs in [("raw", oof_stack), ("platt", oof_stack_platt), ("isotonic", oof_stack_iso)]:
    prob_true, prob_pred = calibration_curve(y, probs, n_bins=10, strategy='quantile')
    fig, ax = plt.subplots(figsize=(6,6))
    ax.plot(prob_pred, prob_true, marker="o", label=name)
    ax.plot([0,1],[0,1],"--", color="gray")
    ax.set_xlabel("Mean predicted prob")
    ax.set_ylabel("Fraction positives")
    ax.set_title(f"Calibration ({name})")
    ax.legend()
    fig.savefig(os.path.join(OUT, f"calibration_{name}.png"), dpi=150)
    plt.close(fig)

# ---------- SHAP (global + small local) ----------
try:
    from lightgbm import LGBMClassifier
    lgb_skl = LGBMClassifier(n_estimators=300, num_leaves=48, max_depth=7, learning_rate=0.05, random_state=42)
    lgb_skl.fit(X, y)
    expl = shap.TreeExplainer(lgb_skl, feature_perturbation="interventional")
    shap_vals = expl.shap_values(X, check_additivity=False)
    shap_pos = shap_vals[1] if isinstance(shap_vals, list) and len(shap_vals) == 2 else shap_vals
    shap_mean = np.abs(shap_pos).mean(axis=0)
    shap_df = pd.DataFrame({"feature": feature_names, "shap_mean_abs": shap_mean}).sort_values("shap_mean_abs", ascending=False)
    shap_df.head(20).to_csv(os.path.join(OUT, "shap_global_top20.csv"), index=False)
    plt.figure(figsize=(7,5))
    shap.summary_plot(shap_pos, features=X_df, feature_names=feature_names, show=False, max_display=20)
    plt.savefig(os.path.join(OUT, "shap_beeswarm.png"), dpi=150, bbox_inches="tight")
    plt.close()
    # local - top 5 highest-risk patients
    top_idx = np.argsort(-oof_stack)[:5]
    local_dir = os.path.join(OUT, "local_shap"); os.makedirs(local_dir, exist_ok=True)
    for i, idx in enumerate(top_idx, start=1):
        vals = shap_pos[idx]
        inds = np.argsort(-np.abs(vals))[:8]
        feats = [feature_names[j] for j in inds]
        contribs = vals[inds]
        fig, ax = plt.subplots(figsize=(6,4))
        colors = ["#d62728" if v>0 else "#1f77b4" for v in contribs]
        ax.barh(range(len(contribs))[::-1], contribs[::-1], color=colors[::-1])
        ax.set_yticks(range(len(contribs)))
        ax.set_yticklabels(feats[::-1])
        ax.set_xlabel("SHAP value (impact on log-odds)")
        ax.set_title(f"Local SHAP - top patient #{i}")
        fig.savefig(os.path.join(local_dir, f"local_shap_top{i}.png"), dpi=150, bbox_inches="tight")
        plt.close(fig)
    print("Saved SHAP artifacts.")
except Exception as e:
    print("SHAP skipped (error):", e)

# ---------- Permutation importance ----------
try:
    perm = permutation_importance(lgb_skl, X, y, n_repeats=10, random_state=42, scoring="roc_auc", n_jobs=-1)
    perm_df = pd.DataFrame({"feature": feature_names, "perm_mean": perm.importances_mean, "perm_std": perm.importances_std}).sort_values("perm_mean", ascending=False)
    perm_df.to_csv(os.path.join(OUT, "perm_importance.csv"), index=False)
    plt.figure(figsize=(8,6))
    sns.barplot(x="perm_mean", y="feature", data=perm_df.head(20))
    plt.title("Permutation importance (top20)")
    plt.tight_layout()
    plt.savefig(os.path.join(OUT,"perm_imp_top20.png"), dpi=150)
    plt.close()
    print("Saved permutation importance.")
except Exception as e:
    print("Permutation importance skipped (error):", e)

# ---------- Save models + artifacts ----------
joblib.dump({"base_models": base_models, "meta": meta, "platt": platt, "isotonic": iso}, os.path.join(OUT, "models_bundle.joblib"))
shap_df.to_csv(os.path.join(OUT, "shap_global.csv"), index=False)
print("\nFinished. All artifacts in:", OUT)
