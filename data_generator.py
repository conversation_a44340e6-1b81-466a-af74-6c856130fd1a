"""
data_generator.py (improved, leak-free)

Creates:
 - data/synthetic_patients_timeseries.csv   (timeseries rows)
 - data/synthetic_patients_last_record.csv  (one row per patient, aggregates for modeling)

Design goals:
 - Realistic clinical signals for chronic disease (HF, diabetes, COPD)
 - Label (deterioration within 90 days) only set on *last record* rows
 - No explicit 'probability' or '_raw_score' features that leak label
 - Include engineered aggregates (slopes, means, min/max) typical for modeling
"""

import os
import random
from datetime import datetime, timedelta
import numpy as np
import pandas as pd

# ---------------- Config ----------------
OUT_DIR = "data"
TIMESERIES_OUT = os.path.join(OUT_DIR, "synthetic_patients_timeseries.csv")
LAST_OUT = os.path.join(OUT_DIR, "synthetic_patients_last_record.csv")

SEED = 42
N_PATIENTS = 2000
MIN_RECORDS = 2
MAX_RECORDS = 8
START_DATE = datetime(2024, 1, 1)

CONDITIONS = ["heart_failure", "diabetes", "copd"]
SEXES = ["M", "F"]
MED_CLASSES = ["beta_blocker", "diuretic", "inhaler", "metformin", "ace_inhibitor", "statin"]
# ---------------------------------------

random.seed(SEED)
np.random.seed(SEED)

def synthetic_visit(pid, idx, base_age, sex, condition, start_date, trend_factors):
    """
    Generate a single visit row for patient.
    trend_factors: dict with baseline + drift info for features to create realistic slopes across visits.
    """
    # record date spaced ~30 days + jitter
    rec_date = start_date + timedelta(days=30 * idx + random.randint(-3, 7))

    # vitals & labs - baseline + noise + slow drift
    age = base_age
    bp_sys = int(np.clip(115 + np.random.randn()*12 + trend_factors.get("bp_sys_drift",0)*idx, 80, 200))
    bp_dia = int(np.clip(75 + np.random.randn()*7 + trend_factors.get("bp_dia_drift",0)*idx, 40, 120))
    hr = int(np.clip(70 + np.random.randn()*10 + trend_factors.get("hr_drift",0)*idx, 40, 160))
    spo2 = round(np.clip(98 + np.random.randn()*1.5 - (0.6 if condition=="copd" else 0) + trend_factors.get("spo2_drift",0)*idx, 80, 100),1)
    weight_kg = round(np.clip(72 + np.random.randn()*11 + trend_factors.get("weight_drift",0)*idx, 40, 160),1)
    hba1c = round(np.clip(5.7 + (1.8 if condition=="diabetes" else 0) + np.random.randn()*0.6 + trend_factors.get("hba1c_drift",0)*idx, 4.0, 15),1)
    egfr = round(np.clip(80 + np.random.randn()*15 - (10 if random.random() < 0.08 else 0) + trend_factors.get("egfr_drift",0)*idx, 5, 120),1)
    bnp = int(np.clip(60 + (200 if condition=="heart_failure" and idx>0 else 0) + np.random.randn()*60 + trend_factors.get("bnp_drift",0)*idx, 5, 5000))

    # medication adherence & refill behavior
    med_adherence_pct = int(np.clip(85 + np.random.randn()*10 - (20 if random.random() < 0.12 and condition=="heart_failure" else 0), 5, 100))
    med_class = random.choice(MED_CLASSES)
    med_refill_gap_days = int(np.clip(np.random.poisson(5) + (10 if med_adherence_pct < 65 else 0), 0, 365))

    # utilization history (prior to this record)
    prior_ed_visits_90d = int(np.random.poisson(0.3))
    prior_admissions_1yr = int(np.random.poisson(0.4))

    return {
        "patient_id": f"P{100000+pid}",
        "record_date": rec_date.date().isoformat(),
        "age": age,
        "sex": sex,
        "condition": condition,
        "bp_sys": bp_sys,
        "bp_dia": bp_dia,
        "hr": hr,
        "spo2": spo2,
        "weight_kg": weight_kg,
        "hba1c": hba1c,
        "egfr": egfr,
        "bnp": bnp,
        "med_class": med_class,
        "med_adherence_pct": med_adherence_pct,
        "med_refill_gap_days": med_refill_gap_days,
        "prior_ed_visits_90d": prior_ed_visits_90d,
        "prior_admissions_1yr": prior_admissions_1yr
    }

def label_patient(last_row, agg_features):
    """
    Label: deterioration within 90d (binary).
    Determined only on last-record aggregates, using plausible clinical heuristics + randomness.
    Do NOT return any 'prob' fields.
    """
    condition = last_row["condition"]
    bnp = agg_features["max_bnp"]
    mean_adherence = agg_features["mean_med_adherence"]
    weight_slope = agg_features["slope_weight"]
    min_spo2 = agg_features["min_spo2"]
    hba1c = agg_features["mean_hba1c"]
    prior_ed_90 = agg_features["prior_ed_visits_90d"]
    prior_adm_1yr = agg_features["prior_admissions_1yr"]

    risk_score = 0.0
    # heart failure signals
    if condition == "heart_failure":
        risk_score += 0.35 * (bnp > 300)
        risk_score += 0.15 * (weight_slope > 2.0)   # rising weight
        risk_score += 0.10 * (mean_adherence < 70)
    # COPD signals
    if condition == "copd":
        risk_score += 0.25 * (min_spo2 < 92)
        risk_score += 0.10 * (mean_adherence < 70)
    # Diabetes signals
    if condition == "diabetes":
        risk_score += 0.30 * (hba1c > 9)
        risk_score += 0.08 * (mean_adherence < 70)

    # Utilization
    risk_score += 0.05 * prior_ed_90
    risk_score += 0.06 * prior_adm_1yr

    # add continuous scaling effects
    # higher BNP scale effect
    risk_score += 0.0008 * max(0, bnp - 100)
    # bigger negative egfr effect (if egfr low)
    risk_score += 0.001 * max(0, 60 - agg_features.get("mean_egfr", 80))

    # clip risk between 0 and 0.95 and convert to probability with non-linear transform
    risk_score = float(np.clip(risk_score, 0.0, 1.2))
    # map to probability via logistic-ish transform adding randomness
    prob = 1/(1 + np.exp(-6*(risk_score - 0.25)))  # shifts masses
    # add small patient-level randomness
    prob = np.clip(prob * (0.85 + np.random.rand()*0.3), 0.0, 0.95)

    # draw label
    label = int(np.random.rand() < prob)
    return label

def aggregate_patient_rows(rows):
    """
    Compute last-record aggregates and slopes for features that matter.
    rows: list of dicts ordered in time
    """
    df = pd.DataFrame(rows)
    # ensure sorted by record_date
    df["record_date"] = pd.to_datetime(df["record_date"])
    df = df.sort_values("record_date").reset_index(drop=True)
    n = len(df)

    # slopes computed via simple linear regression (index -> value)
    def slope(series):
        if len(series.dropna()) < 2:
            return 0.0
        x = np.arange(len(series))
        y = series.values
        A = np.vstack([x, np.ones(len(x))]).T
        m, c = np.linalg.lstsq(A, y, rcond=None)[0]
        return float(m)

    agg = {
        "patient_id": df.loc[df.index[-1], "patient_id"],
        "record_date": df.loc[df.index[-1], "record_date"].date().isoformat(),
        "age": int(df["age"].iloc[-1]),
        "sex": df["sex"].iloc[-1],
        "condition": df["condition"].iloc[-1],
        # last-record raw vitals
        "bp_sys": int(df["bp_sys"].iloc[-1]),
        "bp_dia": int(df["bp_dia"].iloc[-1]),
        "hr": int(df["hr"].iloc[-1]),
        "spo2": float(df["spo2"].iloc[-1]),
        "weight_kg": float(df["weight_kg"].iloc[-1]),
        "hba1c": float(df["hba1c"].iloc[-1]),
        "egfr": float(df["egfr"].iloc[-1]),
        "bnp": int(df["bnp"].iloc[-1]),
        "med_class": df["med_class"].mode().iloc[0] if "med_class" in df.columns else "",
        "med_refill_gap_days": int(df["med_refill_gap_days"].iloc[-1]),
        "mean_med_adherence": float(df["med_adherence_pct"].mean()),
        "min_spo2": float(df["spo2"].min()),
        "max_bnp": int(df["bnp"].max()),
        "n_records": n,
        "slope_weight": slope(df["weight_kg"]),
        "slope_bp_sys": slope(df["bp_sys"]),
        "slope_hr": slope(df["hr"]),
        "slope_hba1c": slope(df["hba1c"]),
        "mean_egfr": float(df["egfr"].mean()),
        "mean_hba1c": float(df["hba1c"].mean()),
        "prior_ed_visits_90d": int(df["prior_ed_visits_90d"].iloc[-1]),
        "prior_admissions_1yr": int(df["prior_admissions_1yr"].iloc[-1])
    }
    return agg

def generate_patient(pid):
    # Baseline demographics
    condition = random.choice(CONDITIONS)
    sex = random.choice(SEXES)
    base_age = int(np.clip(55 + np.random.randn()*12, 30, 95))
    n_rec = random.randint(MIN_RECORDS, MAX_RECORDS)
    start_date = START_DATE + timedelta(days=random.randint(0, 90))  # random cohort entry

    # set small drifts depending on condition
    trend = {}
    if condition == "heart_failure":
        trend["weight_drift"] = 0.3 * (1 if random.random() < 0.5 else 0)  # some patients gain weight
        trend["bnp_drift"] = 12 * (1 if random.random() < 0.4 else 0)
    elif condition == "diabetes":
        trend["hba1c_drift"] = 0.2 * (1 if random.random() < 0.4 else 0)
    elif condition == "copd":
        trend["spo2_drift"] = -0.1 * (1 if random.random() < 0.25 else 0)

    rows = []
    for i in range(n_rec):
        r = synthetic_visit(pid, i, base_age, sex, condition, start_date, trend)
        rows.append(r)

    agg = aggregate_patient_rows(rows)
    # label only determined from agg + last row (no explicit leakage)
    label = label_patient(rows[-1], agg)
    agg["deterioration_90d"] = label
    # last-record flag
    for row in rows:
        row["last_record_flag"] = 0
    rows[-1]["last_record_flag"] = 1

    return rows, agg

def generate_dataset(n_patients=N_PATIENTS):
    timeseries_rows = []
    last_rows = []
    for pid in range(1, n_patients+1):
        rows, agg = generate_patient(pid)
        timeseries_rows.extend(rows)
        last_rows.append(agg)
    ts_df = pd.DataFrame(timeseries_rows)
    last_df = pd.DataFrame(last_rows)
    # sort
    ts_df = ts_df.sort_values(["patient_id", "record_date"]).reset_index(drop=True)
    last_df = last_df.sort_values(["patient_id"]).reset_index(drop=True)
    return ts_df, last_df

def main():
    os.makedirs(OUT_DIR, exist_ok=True)
    ts_df, last_df = generate_dataset()
    ts_df.to_csv(TIMESERIES_OUT, index=False)
    last_df.to_csv(LAST_OUT, index=False)
    print("Saved timeseries ->", TIMESERIES_OUT)
    print("Saved last-record aggregates ->", LAST_OUT)
    print("Total rows:", len(ts_df), "Patients:", last_df.shape[0])
    print("Label distribution (patient-level):")
    print(last_df["deterioration_90d"].value_counts(dropna=False))
    # quick stats
    print("\nSample last-record columns:", last_df.columns.tolist()[:20])
    print(last_df.head().T.iloc[:50])

if __name__ == "__main__":
    main()
