# AI-Driven Risk Prediction Engine for Chronic Care Patients

## Overview

This project addresses the critical challenge of predicting **90-day deterioration risk** in patients with chronic conditions such as heart failure, diabetes, and COPD.  
The system combines advanced **machine learning ensembles**, **calibration techniques**, and **explainable AI (XAI)** to provide clinicians with accurate, interpretable, and actionable risk predictions.  

The final prototype includes a **clinician-friendly dashboard** offering both a cohort overview and patient-level details.

---

## Problem Statement

Chronic care patients are at constant risk of sudden deterioration, leading to avoidable emergency visits and hospitalizations.  
Despite access to patient vitals, labs, and medication data, clinicians lack **reliable early-warning systems**.  

Our solution aims to:
- Predict deterioration risk within **90 days**.  
- Provide **transparent explanations** for predictions.  
- Empower clinicians to take **timely, proactive action**.  

---

## Data & Features

### Synthetic Data
- Patient vitals: blood pressure, heart rate, SpO₂, weight  
- Labs: HbA1c, BNP, eGFR  
- Medication adherence and refill gaps  
- Prior ED visits and hospital admissions  

### Feature Engineering
- **Trends (slopes):** weight, BP, HR, HbA1c  
- **Aggregates:** mean adherence, max BNP, min SpO₂  
- **Interactions:** BNP × weight gain, SpO₂ × adherence  
- **Comorbidities:** diabetes, heart failure, COPD  

---

## Model Architecture

The pipeline follows these stages:

```
Patient Data 
   → Preprocessing & Feature Engineering
   → Base Models (LightGBM, XGBoost, Random Forest)
   → Stacking Meta-Model (Logistic Regression)
   → Calibration (Platt Scaling + Isotonic Regression)
   → Evaluation (AUROC, AUPRC, Brier, Confusion Matrix)
   → Explainability (SHAP, Permutation Importance)
   → Clinician Dashboard (Cohort + Patient Detail Views)
```

---

## Novelty of the Approach

1. **Ensemble Stacking**  
   Combined LightGBM, XGBoost, and Random Forest via a logistic regression meta-model for robust predictions.  

2. **Calibration**  
   Applied both Platt scaling and Isotonic regression to ensure well-calibrated probabilities clinicians can trust.  

3. **Rich Evaluation**  
   Reported AUROC, AUPRC, Brier score, confusion matrices at multiple thresholds, top-k cohort metrics, and calibration plots.  

4. **Explainability Beyond SHAP**  
   - Global SHAP importance and local patient-level SHAP drivers.  
   - Permutation importance for robust cross-validation of feature impact.  

5. **Clinician-Friendly Dashboard**  
   - Cohort view: risk scores and triage labels (Urgent, Review, Routine).  
   - Patient detail view: risk percentage, key drivers, trend snapshots, and recommended next actions.  

---

## Results

- **AUROC:** ~0.79 (stacked calibrated model)  
- **AUPRC:** ~0.76  
- **Brier Score:** 0.17  
- **Confusion Matrices:**  

![Confusion Matrix](outputs/final_results_v3/confusion_raw_thr50.png)  

- **Calibration Plots:**  

![Calibration Plot](outputs/final_results_v3/calibration_raw.png)  

- **SHAP Beeswarm:**  

![SHAP Beeswarm](outputs/final_results_v3/shap_beeswarm.png)  

- **Permutation Importance:**  

![Permutation Importance](outputs/final_results_v3/perm_imp_top20.png)  

---

## Explainability (XAI)

- **Global SHAP Analysis:** BNP, weight trend, and medication adherence were top drivers.  
- **Local SHAP Explanations:** For highest-risk patients, clear feature contributions identified (e.g., rising BNP, poor adherence).  
- **Permutation Importance:** Cross-verified key features across folds.  

---

## Dashboard Prototype

- **Cohort View:** Displays all patients with risk scores and triage labels.  
- **Patient Detail View:**  
  - Predicted 90-day risk  
  - Top 3 feature drivers  
  - Trend snapshots (Weight, BNP, SpO₂)  
  - Recommended clinical actions  

Prototype built in **HTML/CSS/JS** for instant usability.  
A **Streamlit version** is provided for live interactive demo.

---

## Reproduction Guide

### Requirements
Install dependencies:
```bash
pip install -r requirements.txt
```

### Steps
1. Generate synthetic dataset:
```bash
python data_generator.py
```
2. Preprocess features:
```bash
python preprocess.py
```
3. Train models, evaluate, and generate outputs:
```bash
python train_and_eval_final_v3.py
```
4. Launch dashboard (Streamlit):
```bash
streamlit run app.py
```

---

## Impact

- Enables **early intervention** for high-risk chronic patients.  
- Supports **explainable decision-making** for clinicians.  
- Demonstrates integration of **ML ensembles + calibration + XAI** in healthcare.  

---

## Limitations & Next Steps

### Limitations
- Uses **synthetic data** for prototyping.  
- Requires validation on **real-world clinical datasets**.  
- Does not yet integrate directly into hospital EHR systems.  

### Next Steps
- Test on real patient data with ethical approval.  
- Expand features to include lifestyle logs and wearables.  
- Integrate with hospital workflows (FHIR/HL7 APIs).  
- Deploy at scale with secure cloud infrastructure.  

---

## Repository Structure

```
.
├── data/                        # Synthetic patient datasets
├── outputs/final_results_v3/    # Evaluation metrics, plots, SHAP, models
├── data_generator.py            # Synthetic data generator
├── preprocess.py                # Preprocessing & feature engineering
├── train_and_eval_final_v3.py   # Model training, evaluation, XAI
├── app.py                       # Streamlit dashboard (prototype)
├── requirements.txt             # Dependencies
└── README.md                    # Project documentation
```
